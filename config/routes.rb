Rails.application.routes.draw do
  # Devise routes for authentication with custom controllers
  devise_for :users, controllers: {
    sessions: "users/sessions",
    registrations: "users/registrations"
  }

  # Root route - Landing page for non-authenticated users
  root "marketing#index"

  # Authenticated dashboard
  get "dashboard", to: "dashboard#index"

  # Health check
  get "up" => "rails/health#show", as: :rails_health_check
end
