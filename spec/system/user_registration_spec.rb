require 'rails_helper'

RSpec.describe "User Registration", type: :system do
  before do
    driven_by(:rack_test)
  end

  describe "new user registration" do
    let(:user_attributes) do
      {
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        email: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123"
      }
    end

    it "successfully creates a new user and tenant" do
      visit new_user_registration_path

      # Fill out the registration form
      fill_in "user_first_name", with: user_attributes[:first_name]
      fill_in "user_last_name", with: user_attributes[:last_name]
      fill_in "user_email", with: user_attributes[:email]
      fill_in "user_password", with: user_attributes[:password]
      fill_in "user_password_confirmation", with: user_attributes[:password_confirmation]
      check "terms"

      # Submit the form
      click_button "Create account"

      # Should redirect to dashboard
      expect(current_path).to eq(dashboard_path)
      expect(page).to have_content("Welcome")

      # Verify user was created
      user = User.find_by(email: user_attributes[:email])
      expect(user).to be_present
      expect(user.first_name).to eq(user_attributes[:first_name])
      expect(user.last_name).to eq(user_attributes[:last_name])
      expect(user.role).to eq("owner") # First user should be owner

      # Verify tenant was created
      expect(user.tenant).to be_present
      expect(user.tenant.name).to eq("#{user_attributes[:first_name]} #{user_attributes[:last_name]}")
      expect(user.tenant.subdomain).to match(/johndoe\d*/)
      expect(user.tenant.status).to eq("active")

      # Verify user-tenant relationship
      expect(user.tenant.users).to include(user)
    end

    it "handles validation errors gracefully" do
      visit new_user_registration_path

      # Submit form with missing required fields
      click_button "Create account"

      # Should stay on registration page with errors
      expect(current_path).to eq(user_registration_path)
      expect(page).to have_content("error")
    end

    it "prevents duplicate email registration" do
      # Create an existing user
      existing_tenant = create(:tenant)
      existing_user = create(:user, email: user_attributes[:email], tenant: existing_tenant)

      visit new_user_registration_path

      # Try to register with same email
      fill_in "user_first_name", with: user_attributes[:first_name]
      fill_in "user_last_name", with: user_attributes[:last_name]
      fill_in "user_email", with: user_attributes[:email]
      fill_in "user_password", with: user_attributes[:password]
      fill_in "user_password_confirmation", with: user_attributes[:password_confirmation]
      check "terms"

      click_button "Create account"

      # Should stay on registration page with error
      expect(current_path).to eq(user_registration_path)
      expect(page).to have_content("has already been taken")
    end

    it "generates unique subdomains for similar names" do
      # Create first user with similar name
      first_tenant = create(:tenant, subdomain: "johndoe")
      first_user = create(:user, first_name: "John", last_name: "Doe", tenant: first_tenant)

      visit new_user_registration_path

      # Register second user with same name
      fill_in "user_first_name", with: "John"
      fill_in "user_last_name", with: "Doe"
      fill_in "user_email", with: "<EMAIL>"
      fill_in "user_password", with: "password123"
      fill_in "user_password_confirmation", with: "password123"
      check "terms"

      click_button "Create account"

      # Should succeed with different subdomain
      expect(current_path).to eq(dashboard_path)
      
      new_user = User.find_by(email: "<EMAIL>")
      expect(new_user.tenant.subdomain).to eq("johndoe1")
    end
  end

  describe "registration form" do
    it "displays all required fields" do
      visit new_user_registration_path

      expect(page).to have_field("user_first_name")
      expect(page).to have_field("user_last_name")
      expect(page).to have_field("user_email")
      expect(page).to have_field("user_password")
      expect(page).to have_field("user_password_confirmation")
      expect(page).to have_field("terms")
      expect(page).to have_button("Create account")
    end

    it "shows password requirements" do
      visit new_user_registration_path

      expect(page).to have_content("Minimum 6 characters")
    end

    it "has proper form validation" do
      visit new_user_registration_path

      # Test that submit button is disabled initially
      submit_button = find("#submit-button")
      expect(submit_button).to be_disabled
    end
  end
end
