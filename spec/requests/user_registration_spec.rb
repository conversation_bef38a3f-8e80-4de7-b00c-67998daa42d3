require 'rails_helper'

RSpec.describe "User Registration", type: :request do
  before do
    # Force reload routes in test environment
    Rails.application.reload_routes!
  end
  describe "Routes" do
    it "has the registration route available" do
      expect(Rails.application.routes.recognize_path("/users", method: :post)).to include(controller: "users/registrations")
    end
  end

  describe "POST /users" do
    let(:valid_attributes) do
      {
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        email: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123"
      }
    end

    it "successfully creates a new user and tenant" do
      post "/users", params: {
        user: valid_attributes,
        terms: "on"
      }

      puts "Response status: #{response.status}"
      puts "Response body: #{response.body}" if response.status != 302
      puts "User count: #{User.count}"
      puts "Tenant count: #{Tenant.count}"

      expect(response).to redirect_to("/dashboard")
      
      user = User.find_by(email: valid_attributes[:email])
      expect(user).to be_present
      expect(user.first_name).to eq(valid_attributes[:first_name])
      expect(user.last_name).to eq(valid_attributes[:last_name])
      expect(user.role).to eq("owner")
      
      expect(user.tenant).to be_present
      expect(user.tenant.name).to eq("#{valid_attributes[:first_name]} #{valid_attributes[:last_name]}")
      expect(user.tenant.status).to eq("active")
    end

    it "handles validation errors gracefully" do
      expect {
        post "/users", params: {
          user: {
            first_name: "",
            last_name: "",
            email: "invalid-email",
            password: "123",
            password_confirmation: "456"
          }
        }
      }.not_to change(User, :count)

      expect(response).to have_http_status(:unprocessable_entity)
    end

    it "prevents duplicate email registration" do
      # Create an existing user
      existing_tenant = create(:tenant)
      create(:user, email: valid_attributes[:email], tenant: existing_tenant)

      expect {
        post "/users", params: {
          user: valid_attributes,
          terms: "on"
        }
      }.not_to change(User, :count)

      expect(response).to have_http_status(:unprocessable_entity)
    end
  end
end
