#!/usr/bin/env ruby

# Manual test script to verify registration functionality
puts "🧪 Manual Registration Test"
puts "=" * 60

# Load Rails environment
require_relative 'config/environment'

# Try development environment first
Rails.env = 'development'

# Explicitly require Devise
require 'devise'

# Reload routes
Rails.application.reload_routes!

puts "\n🔍 Checking Devise and Routes:"

# Check if <PERSON><PERSON> is loaded
begin
  puts "  📦 Devise version: #{Devise::VERSION}"
  puts "  📦 Devise mappings: #{Devise.mappings.keys}"
rescue => e
  puts "  ❌ Devise not loaded: #{e.message}"
  exit 1
end

# Check all routes
puts "  📋 All routes:"
Rails.application.routes.routes.each do |route|
  puts "    #{route.verb} #{route.path.spec} -> #{route.defaults[:controller]}##{route.defaults[:action]}"
end

# Check specific route
begin
  route_info = Rails.application.routes.recognize_path("/users", method: :post)
  puts "  ✅ Route found: #{route_info}"
rescue => e
  puts "  ❌ Route error: #{e.message}"
end

puts "\n🔧 Testing Registration Controller:"

# Test data
user_params = {
  first_name: '<PERSON>',
  last_name: '<PERSON><PERSON>',
  email: '<EMAIL>',
  password: 'password123',
  password_confirmation: 'password123'
}

begin
  # Clean up any existing test data
  existing_user = User.find_by(email: user_params[:email])
  if existing_user
    puts "  🧹 Cleaning up existing test user"
    existing_user.tenant&.destroy
    existing_user.destroy
  end

  # Create a mock request environment
  env = {
    'REQUEST_METHOD' => 'POST',
    'PATH_INFO' => '/users',
    'QUERY_STRING' => '',
    'HTTP_HOST' => 'localhost:3000',
    'rack.input' => StringIO.new,
    'rack.errors' => StringIO.new
  }

  # Create request
  request = ActionDispatch::Request.new(env)
  
  # Create controller instance
  controller = Users::RegistrationsController.new
  controller.request = request
  controller.response = ActionDispatch::Response.new

  # Set up Devise mapping
  request.env["devise.mapping"] = Devise.mappings[:user]

  # Set up parameters
  params = ActionController::Parameters.new({
    user: user_params,
    terms: 'on',
    controller: 'users/registrations',
    action: 'create'
  })
  
  controller.params = params

  puts "  📝 Attempting to create user with controller..."
  
  # Call the create action directly
  result = controller.create

  puts "  ✅ Controller create method executed"
  
  # Check if user was created
  created_user = User.find_by(email: user_params[:email])
  if created_user
    puts "  ✅ User created successfully: #{created_user.full_name}"
    puts "  ✅ User tenant: #{created_user.tenant.name}"
    puts "  ✅ User role: #{created_user.role}"
    
    # Clean up
    puts "  🧹 Cleaning up test data"
    created_user.tenant.destroy
    created_user.destroy
  else
    puts "  ❌ User was not created"
  end

rescue => e
  puts "  ❌ Controller test error: #{e.message}"
  puts "  #{e.backtrace.first}"
end

puts "\n🌐 Testing with Rack App:"

begin
  # Create a Rack test session
  require 'rack/test'
  
  class TestApp
    include Rack::Test::Methods
    
    def app
      Rails.application
    end
  end
  
  test_app = TestApp.new
  
  # Make a POST request
  response = test_app.post '/users', {
    user: user_params,
    terms: 'on'
  }
  
  puts "  📡 Response status: #{response.status}"
  puts "  📡 Response headers: #{response.headers}"
  
  if response.status == 302
    puts "  ✅ Registration successful (redirect)"
  elsif response.status == 422
    puts "  ⚠️  Validation errors (422)"
  else
    puts "  ❌ Unexpected response: #{response.status}"
    puts "  Response body: #{response.body[0..500]}"
  end

rescue => e
  puts "  ❌ Rack test error: #{e.message}"
  puts "  #{e.backtrace.first}"
end

puts "\n" + "=" * 60
puts "🏁 Manual Registration Test Complete"
puts "=" * 60
